#include "pch.h"
#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"

// ===========================
// IMPROVED AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot(const Reader& reader)
{
    if (!globals::Legitbot::enabled) {
        return;
    }

    // Handle aim key state
    bool currentAimKeyState = GetAsyncKeyState(0x58) & 0x8000; // X key
    aimKeyPressed = currentAimKeyState;

    // Set update flag when key is first pressed
    if (currentAimKeyState && !lastAimKeyState) {
        updateTarget = true;
        accumulatedX = 0.0f;
        accumulatedY = 0.0f;
    }

    lastAimKeyState = currentAimKeyState;

    if (aimKeyPressed) {
        // Get best target using the preferred GetTarget function
        AimTarget target = GetTarget(reader);

        if (target.isValid && updateTarget) {
            currentTarget = target;
            targetAcquired = true;
            updateTarget = false; // Set to false after acquiring target as per user preference
        }

        // Aim at current target if we have one
        if (targetAcquired && currentTarget.isValid) {
            improvedMouseMove(currentTarget.screenPos);
        }
    } else {
        // Reset when key is released
        targetAcquired = false;
        updateTarget = true;
        accumulatedX = 0.0f;
        accumulatedY = 0.0f;
    }
}

AimTarget Aimbot::GetTarget(const Reader& reader)
{
    Vector closestTarget = findClosestTarget(reader);

    if (closestTarget.x == 0.0f && closestTarget.y == 0.0f && closestTarget.z == 0.0f) {
        return AimTarget(); // Invalid target
    }

    // Calculate distance from center of screen
    Vector centerScreen{
        static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2.0f,
        static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2.0f,
        0.0f
    };

    float dx = closestTarget.x - centerScreen.x;
    float dy = closestTarget.y - centerScreen.y;
    float distance = sqrt(dx * dx + dy * dy);

    return AimTarget(closestTarget, distance);
}

Vector Aimbot::findClosestTarget(const Reader& reader)
{
    view_matrix_t viewMatrix = GameData::getViewMatrix();
    auto playerList = reader.getPlayerListCopy();

    Vector centerScreen{
        static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2.0f,
        static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2.0f,
        0.0f
    };

    float maxDistanceSq = globals::Legitbot::radius * globals::Legitbot::radius;
    float closestDistanceSq = FLT_MAX;
    Vector bestTarget{0, 0, 0};

    int localTeam = GameData::getLocalTeam();

    for (const auto& player : playerList) {
        // Skip teammates if team check is enabled
        if (player.team == localTeam && globals::Legitbot::teamcheck) {
            continue;
        }

        // Skip if visibility check is enabled and player is not spotted
        if (globals::Legitbot::visiblecheck && !player.enemySpotted) {
            continue;
        }

        // Get head position
        Vector playerPosition = driver::read_memory<Vector>(
            GameVars::getInstance()->getDriver(),
            player.BoneArray + bones::head * 32
        );

        // Convert to screen coordinates
        Vector screenPos;
        if (Vector::world_to_screen(viewMatrix, playerPosition, screenPos)) {
            float dx = screenPos.x - centerScreen.x;
            float dy = screenPos.y - centerScreen.y;
            float distanceSq = dx * dx + dy * dy;

            // Check if target is within radius and closer than current best
            if (distanceSq < closestDistanceSq && distanceSq <= maxDistanceSq) {
                closestDistanceSq = distanceSq;
                bestTarget = screenPos;
            }
        }
    }

    return bestTarget;
}

void Aimbot::improvedMouseMove(const Vector& targetPos)
{
    if (targetPos.x == 0.0f && targetPos.y == 0.0f && targetPos.z == 0.0f) {
        return;
    }

    POINT currentMousePos;
    GetCursorPos(&currentMousePos);

    float deltaX = targetPos.x - static_cast<float>(currentMousePos.x);
    float deltaY = targetPos.y - static_cast<float>(currentMousePos.y);

    // Calculate distance for adaptive smoothing
    float distance = sqrt(deltaX * deltaX + deltaY * deltaY);

    // Minimum smoothness to prevent division by zero
    float smoothness = (globals::Legitbot::smoothness > 1.0f) ? globals::Legitbot::smoothness : 1.0f;

    // Adaptive smoothing based on distance - closer targets get more smoothing
    float adaptiveFactor = 1.0f + (distance / 500.0f);
    float finalSmoothness = smoothness * adaptiveFactor;

    // Calculate movement step
    float stepX = deltaX / finalSmoothness;
    float stepY = deltaY / finalSmoothness;

    // Accumulate fractional movements for precision
    accumulatedX += stepX;
    accumulatedY += stepY;

    // Extract integer movement
    int moveX = static_cast<int>(accumulatedX + 0.5f); // Simple rounding
    int moveY = static_cast<int>(accumulatedY + 0.5f);

    // Subtract moved amount from accumulator
    accumulatedX -= static_cast<float>(moveX);
    accumulatedY -= static_cast<float>(moveY);

    // Apply deadzone to prevent micro-movements
    float deadzone = (smoothness * 0.1f > 1.0f) ? smoothness * 0.1f : 1.0f;
    if (distance < deadzone) {
        return;
    }

    // Use SendInput for better mouse movement
    if (moveX != 0 || moveY != 0) {
        INPUT input = {};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = MOUSEEVENTF_MOVE;
        input.mi.dx = moveX;
        input.mi.dy = moveY;

        SendInput(1, &input, sizeof(INPUT));
    }
}