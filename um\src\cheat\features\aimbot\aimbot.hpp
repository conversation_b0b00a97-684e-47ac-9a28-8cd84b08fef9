#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"

#include <thread>
#include <chrono>
#include <Windows.h>


struct AimTarget {
    Vector screenPos;
    float distance;
    bool isValid;

    AimTarget() : screenPos(0, 0, 0), distance(FLT_MAX), isValid(false) {}
    AimTarget(const Vector& pos, float dist) : screenPos(pos), distance(dist), isValid(true) {}
};

class Aimbot
{
public:
    void doAimbot(const Reader& reader);

    // Preferred interface as per user requirements
    AimTarget GetTarget(const Reader& reader);

private:
    // Core functionality - simplified approach
    Vector findClosestTarget(const Reader& reader);
    void improvedMouseMove(const Vector& targetPos);

    // State management
    bool aimKeyPressed = false;
    bool lastAimKeyState = false;
    bool targetAcquired = false;
    bool updateTarget = true;  // Update boolean as per user preference

    // Smoothing variables
    float accumulatedX = 0.0f;
    float accumulatedY = 0.0f;

    // Current target tracking
    AimTarget currentTarget;
};

inline Aimbot aimbot;